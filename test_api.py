#!/usr/bin/env python
"""
API Testing Script for Email Service.
This script tests all the API endpoints to ensure they work correctly.
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class EmailServiceAPITester:
    """Test suite for Email Service API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.token = None
        self.user_id = None
        self.domain_id = None
        
    def print_test(self, test_name: str, success: bool, details: str = ""):
        """Print test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
    
    def make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None, 
                    auth: bool = False) -> tuple[bool, Dict[str, Any]]:
        """Make HTTP request to API."""
        url = f"{self.base_url}{endpoint}"
        headers = {"Content-Type": "application/json"}
        
        if auth and self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=data)
            elif method == "PUT":
                response = requests.put(url, headers=headers, json=data)
            elif method == "DELETE":
                response = requests.delete(url, headers=headers)
            else:
                return False, {"error": f"Unsupported method: {method}"}
            
            return response.status_code < 400, response.json()
            
        except requests.exceptions.ConnectionError:
            return False, {"error": "Connection failed - is the server running?"}
        except Exception as e:
            return False, {"error": str(e)}
    
    def test_health_check(self) -> bool:
        """Test health check endpoint."""
        success, response = self.make_request("GET", "/health/")
        self.print_test("Health Check", success, 
                       f"Status: {response.get('status', 'unknown')}")
        return success
    
    def test_user_registration(self) -> bool:
        """Test user registration."""
        test_user = {
            "email": "<EMAIL>",
            "first_name": "Test",
            "last_name": "User",
            "password": "testpassword123",
            "company_name": "Test Company"
        }
        
        success, response = self.make_request("POST", "/api/v1/accounts/register/", test_user)
        
        if success:
            self.token = response.get("token")
            self.user_id = response.get("user_id")
            self.print_test("User Registration", True, 
                           f"User ID: {self.user_id}")
        else:
            self.print_test("User Registration", False, 
                           f"Error: {response.get('error', 'Unknown error')}")
        
        return success
    
    def test_user_login(self) -> bool:
        """Test user login."""
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        success, response = self.make_request("POST", "/api/v1/accounts/login/", login_data)
        
        if success:
            self.token = response.get("token")
            self.print_test("User Login", True, "Token received")
        else:
            self.print_test("User Login", False, 
                           f"Error: {response.get('error', 'Unknown error')}")
        
        return success
    
    def test_get_profile(self) -> bool:
        """Test get user profile."""
        success, response = self.make_request("GET", "/api/v1/accounts/profile/", auth=True)
        
        if success:
            user = response.get("user", {})
            self.print_test("Get Profile", True, 
                           f"Email: {user.get('email', 'N/A')}")
        else:
            self.print_test("Get Profile", False, 
                           f"Error: {response.get('error', 'Unknown error')}")
        
        return success
    
    def test_dashboard_stats(self) -> bool:
        """Test dashboard statistics."""
        success, response = self.make_request("GET", "/api/v1/accounts/dashboard/stats/", auth=True)
        
        if success:
            stats = response.get("stats", {})
            self.print_test("Dashboard Stats", True, 
                           f"Domains: {stats.get('total_domains', 0)}")
        else:
            self.print_test("Dashboard Stats", False, 
                           f"Error: {response.get('error', 'Unknown error')}")
        
        return success
    
    def test_create_domain(self) -> bool:
        """Test domain creation."""
        domain_data = {
            "name": "example.com"
        }
        
        success, response = self.make_request("POST", "/api/v1/domains/", domain_data, auth=True)
        
        if success:
            domain = response.get("domain", {})
            self.domain_id = domain.get("id")
            self.print_test("Create Domain", True, 
                           f"Domain: {domain.get('name', 'N/A')}")
        else:
            self.print_test("Create Domain", False, 
                           f"Error: {response.get('error', 'Unknown error')}")
        
        return success
    
    def test_list_domains(self) -> bool:
        """Test listing domains."""
        success, response = self.make_request("GET", "/api/v1/domains/", auth=True)
        
        if success:
            domains = response.get("domains", [])
            self.print_test("List Domains", True, 
                           f"Found {len(domains)} domain(s)")
        else:
            self.print_test("List Domains", False, 
                           f"Error: {response.get('error', 'Unknown error')}")
        
        return success
    
    def test_get_domain_details(self) -> bool:
        """Test getting domain details."""
        if not self.domain_id:
            self.print_test("Get Domain Details", False, "No domain ID available")
            return False
        
        success, response = self.make_request("GET", f"/api/v1/domains/{self.domain_id}/", auth=True)
        
        if success:
            domain = response.get("domain", {})
            self.print_test("Get Domain Details", True, 
                           f"Status: {domain.get('status', 'N/A')}")
        else:
            self.print_test("Get Domain Details", False, 
                           f"Error: {response.get('error', 'Unknown error')}")
        
        return success
    
    def test_get_dns_records(self) -> bool:
        """Test getting DNS records for domain."""
        if not self.domain_id:
            self.print_test("Get DNS Records", False, "No domain ID available")
            return False
        
        success, response = self.make_request("GET", f"/api/v1/domains/{self.domain_id}/dns/", auth=True)
        
        if success:
            records = response.get("dns_records", [])
            self.print_test("Get DNS Records", True, 
                           f"Found {len(records)} DNS record(s)")
        else:
            self.print_test("Get DNS Records", False, 
                           f"Error: {response.get('error', 'Unknown error')}")
        
        return success
    
    def run_all_tests(self) -> bool:
        """Run all API tests."""
        print("🧪 Starting Email Service API Tests")
        print("=" * 50)
        
        tests = [
            self.test_health_check,
            self.test_user_registration,
            self.test_user_login,
            self.test_get_profile,
            self.test_dashboard_stats,
            self.test_create_domain,
            self.test_list_domains,
            self.test_get_domain_details,
            self.test_get_dns_records,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                time.sleep(0.5)  # Small delay between tests
            except Exception as e:
                print(f"❌ FAIL {test.__name__} - Exception: {e}")
        
        print("\n" + "=" * 50)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Your API is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
        
        return passed == total


def main():
    """Main function to run API tests."""
    import sys
    
    # Check if server URL is provided
    base_url = "http://localhost:8000"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"🌐 Testing API at: {base_url}")
    
    tester = EmailServiceAPITester(base_url)
    success = tester.run_all_tests()
    
    if not success:
        print("\n💡 Troubleshooting Tips:")
        print("1. Make sure the development server is running:")
        print("   python run_dev_server.py")
        print("2. Check if Firebase is properly configured")
        print("3. Verify .env file settings")
        print("4. Check server logs for errors")
    
    return 0 if success else 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
