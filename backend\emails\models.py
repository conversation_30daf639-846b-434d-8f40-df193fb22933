"""
Email account models using Firestore as backend.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import re


class EmailAccountStatus(Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    PENDING = "pending"
    DISABLED = "disabled"


class EmailAccountType(Enum):
    REGULAR = "regular"
    ALIAS = "alias"
    CATCH_ALL = "catch_all"
    FORWARD_ONLY = "forward_only"


@dataclass
class EmailAccount:
    """Email account model for Firestore."""
    email: str
    domain_id: str
    owner_id: str
    account_type: EmailAccountType = EmailAccountType.REGULAR
    status: EmailAccountStatus = EmailAccountStatus.PENDING
    password_hash: Optional[str] = None
    quota_mb: int = 1024  # 1GB default
    used_storage_mb: int = 0
    forward_to: Optional[str] = None
    auto_reply_enabled: bool = False
    auto_reply_message: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Firestore."""
        data = {
            'email': self.email,
            'domain_id': self.domain_id,
            'owner_id': self.owner_id,
            'account_type': self.account_type.value,
            'status': self.status.value,
            'quota_mb': self.quota_mb,
            'used_storage_mb': self.used_storage_mb,
            'forward_to': self.forward_to,
            'auto_reply_enabled': self.auto_reply_enabled,
            'auto_reply_message': self.auto_reply_message,
        }
        
        if self.password_hash:
            data['password_hash'] = self.password_hash
        if self.created_at:
            data['created_at'] = self.created_at
        if self.updated_at:
            data['updated_at'] = self.updated_at
        if self.last_login:
            data['last_login'] = self.last_login
            
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], doc_id: str = None) -> 'EmailAccount':
        """Create EmailAccount from Firestore document."""
        return cls(
            id=doc_id,
            email=data['email'],
            domain_id=data['domain_id'],
            owner_id=data['owner_id'],
            account_type=EmailAccountType(data.get('account_type', 'regular')),
            status=EmailAccountStatus(data.get('status', 'pending')),
            password_hash=data.get('password_hash'),
            quota_mb=data.get('quota_mb', 1024),
            used_storage_mb=data.get('used_storage_mb', 0),
            forward_to=data.get('forward_to'),
            auto_reply_enabled=data.get('auto_reply_enabled', False),
            auto_reply_message=data.get('auto_reply_message'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at'),
            last_login=data.get('last_login'),
        )
    
    @property
    def username(self) -> str:
        """Get username part of email."""
        return self.email.split('@')[0]
    
    @property
    def domain_name(self) -> str:
        """Get domain part of email."""
        return self.email.split('@')[1]
    
    @property
    def is_active(self) -> bool:
        """Check if account is active."""
        return self.status == EmailAccountStatus.ACTIVE
    
    @property
    def storage_usage_percent(self) -> float:
        """Get storage usage percentage."""
        if self.quota_mb == 0:
            return 0.0
        return (self.used_storage_mb / self.quota_mb) * 100
    
    @property
    def is_over_quota(self) -> bool:
        """Check if account is over quota."""
        return self.used_storage_mb > self.quota_mb
    
    def can_receive_email(self) -> bool:
        """Check if account can receive emails."""
        return (
            self.is_active and 
            not self.is_over_quota and 
            self.account_type != EmailAccountType.FORWARD_ONLY
        )
    
    def can_send_email(self) -> bool:
        """Check if account can send emails."""
        return (
            self.is_active and 
            self.account_type != EmailAccountType.ALIAS and
            self.password_hash is not None
        )
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """Validate email format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def is_valid_username(username: str) -> bool:
        """Validate username part of email."""
        # Username can contain letters, numbers, dots, hyphens, underscores
        pattern = r'^[a-zA-Z0-9._-]+$'
        return bool(re.match(pattern, username)) and len(username) <= 64


@dataclass
class EmailAlias:
    """Email alias model."""
    alias_email: str
    target_email: str
    domain_id: str
    owner_id: str
    enabled: bool = True
    created_at: Optional[datetime] = None
    id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Firestore."""
        data = {
            'alias_email': self.alias_email,
            'target_email': self.target_email,
            'domain_id': self.domain_id,
            'owner_id': self.owner_id,
            'enabled': self.enabled,
        }
        
        if self.created_at:
            data['created_at'] = self.created_at
            
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], doc_id: str = None) -> 'EmailAlias':
        """Create EmailAlias from Firestore document."""
        return cls(
            id=doc_id,
            alias_email=data['alias_email'],
            target_email=data['target_email'],
            domain_id=data['domain_id'],
            owner_id=data['owner_id'],
            enabled=data.get('enabled', True),
            created_at=data.get('created_at'),
        )


@dataclass
class EmailLog:
    """Email log entry for tracking email activity."""
    account_id: str
    action: str  # send, receive, login, logout
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Firestore."""
        return {
            'account_id': self.account_id,
            'action': self.action,
            'timestamp': self.timestamp,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'success': self.success,
            'error_message': self.error_message,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], doc_id: str = None) -> 'EmailLog':
        """Create EmailLog from Firestore document."""
        return cls(
            id=doc_id,
            account_id=data['account_id'],
            action=data['action'],
            timestamp=data['timestamp'],
            details=data.get('details', {}),
            ip_address=data.get('ip_address'),
            user_agent=data.get('user_agent'),
            success=data.get('success', True),
            error_message=data.get('error_message'),
        )


@dataclass
class EmailQuota:
    """Email quota tracking."""
    account_id: str
    quota_mb: int
    used_mb: int
    last_updated: datetime
    
    @property
    def usage_percent(self) -> float:
        """Get usage percentage."""
        if self.quota_mb == 0:
            return 0.0
        return (self.used_mb / self.quota_mb) * 100
    
    @property
    def is_over_quota(self) -> bool:
        """Check if over quota."""
        return self.used_mb > self.quota_mb
    
    @property
    def available_mb(self) -> int:
        """Get available storage in MB."""
        return max(0, self.quota_mb - self.used_mb)
