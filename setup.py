#!/usr/bin/env python3
"""
Email Hosting Service Setup Script
This script helps you set up your email hosting service step by step.
"""

import os
import sys
import subprocess
import secrets
import string
from pathlib import Path

def generate_secret_key(length=50):
    """Generate a secure random secret key."""
    alphabet = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def generate_password(length=16):
    """Generate a secure random password."""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def create_env_file():
    """Create .env file from template with generated secrets."""
    print("🔧 Creating environment configuration...")
    
    # Get domain from user
    domain = input("Enter your domain name (e.g., yourdomain.com): ").strip()
    if not domain:
        print("❌ Domain is required!")
        sys.exit(1)
    
    admin_email = input(f"Enter admin email (default: admin@{domain}): ").strip()
    if not admin_email:
        admin_email = f"admin@{domain}"
    
    # Generate secure passwords
    postgres_password = generate_password(20)
    redis_password = generate_password(20)
    secret_key = generate_secret_key(50)
    grafana_password = generate_password(16)
    
    # Read template and replace values
    with open('.env.example', 'r') as f:
        env_content = f.read()
    
    env_content = env_content.replace('yourdomain.com', domain)
    env_content = env_content.replace('your_secure_postgres_password_here', postgres_password)
    env_content = env_content.replace('your_secure_redis_password_here', redis_password)
    env_content = env_content.replace('your_django_secret_key_here_make_it_long_and_random', secret_key)
    env_content = env_content.replace('<EMAIL>', admin_email)
    env_content = env_content.replace('your_grafana_admin_password', grafana_password)
    
    # Write .env file
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print(f"✅ Environment file created!")
    print(f"📧 Domain: {domain}")
    print(f"👤 Admin Email: {admin_email}")
    print(f"🔐 Grafana Password: {grafana_password}")
    print("⚠️  Please save these credentials securely!")

def check_dependencies():
    """Check if required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    dependencies = ['docker', 'docker-compose']
    missing = []
    
    for dep in dependencies:
        try:
            subprocess.run([dep, '--version'], capture_output=True, check=True)
            print(f"✅ {dep} is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {dep} is not installed")
            missing.append(dep)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("Please install Docker and Docker Compose before continuing.")
        print("Visit: https://docs.docker.com/get-docker/")
        sys.exit(1)
    
    print("✅ All dependencies are installed!")

def create_ssl_directory():
    """Create SSL directory and instructions."""
    ssl_dir = Path('ssl')
    ssl_dir.mkdir(exist_ok=True)
    
    with open('ssl/README.md', 'w') as f:
        f.write("""# SSL Certificates

Place your SSL certificates in this directory:

- `cert.pem` - Your SSL certificate
- `private.key` - Your private key
- `chain.pem` - Certificate chain (if applicable)

## Getting SSL Certificates

### Option 1: Let's Encrypt (Recommended)
```bash
# Install certbot
sudo apt-get install certbot

# Get certificate
sudo certbot certonly --standalone -d mail.yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/mail.yourdomain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/mail.yourdomain.com/privkey.pem ssl/private.key
```

### Option 2: Self-Signed (Development Only)
```bash
# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout ssl/private.key -out ssl/cert.pem -days 365 -nodes
```

### Option 3: Commercial Certificate
Upload your purchased SSL certificate files to this directory.
""")
    
    print("📁 SSL directory created with instructions")

def create_database_init():
    """Create database initialization script."""
    init_sql = """
-- Email Service Database Initialization

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";

-- Create domains table
CREATE TABLE IF NOT EXISTS domains (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name CITEXT UNIQUE NOT NULL,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email CITEXT UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    domain_id UUID REFERENCES domains(id) ON DELETE CASCADE,
    quota_mb INTEGER DEFAULT 1024,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create aliases table
CREATE TABLE IF NOT EXISTS aliases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source CITEXT NOT NULL,
    destination CITEXT NOT NULL,
    domain_id UUID REFERENCES domains(id) ON DELETE CASCADE,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_domain ON users(domain_id);
CREATE INDEX IF NOT EXISTS idx_aliases_source ON aliases(source);
CREATE INDEX IF NOT EXISTS idx_aliases_domain ON aliases(domain_id);

-- Insert default admin domain (will be updated by setup)
INSERT INTO domains (name) VALUES ('localhost') ON CONFLICT DO NOTHING;
"""
    
    with open('database/init.sql', 'w') as f:
        f.write(init_sql)
    
    print("🗄️ Database initialization script created")

def main():
    """Main setup function."""
    print("🚀 Email Hosting Service Setup")
    print("=" * 40)
    
    # Check if .env already exists
    if os.path.exists('.env'):
        overwrite = input("⚠️  .env file already exists. Overwrite? (y/N): ").strip().lower()
        if overwrite != 'y':
            print("Setup cancelled.")
            sys.exit(0)
    
    try:
        check_dependencies()
        create_env_file()
        create_ssl_directory()
        create_database_init()
        
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Configure your DNS records (MX, A, SPF, DKIM)")
        print("2. Obtain SSL certificates and place them in the ssl/ directory")
        print("3. Run: docker-compose up -d")
        print("4. Access admin panel at: http://localhost:8000")
        print("5. Access webmail at: http://localhost:3000")
        print("\n📖 See README.md for detailed instructions")
        
    except KeyboardInterrupt:
        print("\n❌ Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
