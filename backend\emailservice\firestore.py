"""
Firestore database service layer for email hosting platform.
"""

import os
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from google.cloud import firestore
from google.auth.exceptions import DefaultCredentialsError
import firebase_admin
from firebase_admin import credentials, firestore as admin_firestore

logger = logging.getLogger(__name__)


class FirestoreService:
    """
    Centralized Firestore service for all database operations.
    """
    
    _instance = None
    _db = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._db is None:
            self._initialize_firestore()
    
    def _initialize_firestore(self):
        """Initialize Firestore connection."""
        try:
            # Initialize Firebase Admin SDK
            if not firebase_admin._apps:
                cred_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
                if cred_path and os.path.exists(cred_path):
                    cred = credentials.Certificate(cred_path)
                    firebase_admin.initialize_app(cred)
                    logger.info("Using Firebase credentials from file")
                else:
                    logger.warning(f"Firebase credentials file not found: {cred_path}")
                    logger.warning("Firestore operations will be mocked for development")
                    self._db = None
                    return

            self._db = admin_firestore.client()
            logger.info("Firestore initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Firestore: {e}")
            logger.warning("Firestore operations will be mocked for development")
            self._db = None
    
    @property
    def db(self):
        """Get Firestore database instance."""
        if self._db is None:
            self._initialize_firestore()
        return self._db

    def _mock_response(self, data=None):
        """Return mock response when Firestore is not available."""
        if data is None:
            data = {"id": "mock-id", "status": "development"}
        logger.info(f"Mock Firestore operation: {data}")
        return data
    
    # User Management
    def create_user(self, user_data: Dict[str, Any]) -> str:
        """Create a new user in Firestore."""
        if self.db is None:
            mock_id = f"mock-user-{hash(user_data.get('email', 'test'))}"
            return self._mock_response({"id": mock_id, "email": user_data.get('email')})["id"]

        try:
            user_data['created_at'] = datetime.utcnow()
            user_data['updated_at'] = datetime.utcnow()

            doc_ref = self.db.collection('users').document()
            doc_ref.set(user_data)

            logger.info(f"User created with ID: {doc_ref.id}")
            return doc_ref.id

        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise
    
    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID."""
        try:
            doc = self.db.collection('users').document(user_id).get()
            if doc.exists:
                data = doc.to_dict()
                data['id'] = doc.id
                return data
            return None
            
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            raise
    
    def update_user(self, user_id: str, update_data: Dict[str, Any]) -> bool:
        """Update user data."""
        try:
            update_data['updated_at'] = datetime.utcnow()
            
            self.db.collection('users').document(user_id).update(update_data)
            logger.info(f"User {user_id} updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            return False
    
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email address."""
        try:
            query = self.db.collection('users').where('email', '==', email).limit(1)
            docs = query.stream()
            
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                return data
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            raise
    
    # Domain Management
    def create_domain(self, domain_data: Dict[str, Any]) -> str:
        """Create a new domain."""
        try:
            domain_data['created_at'] = datetime.utcnow()
            domain_data['updated_at'] = datetime.utcnow()
            domain_data['status'] = 'pending'
            
            doc_ref = self.db.collection('domains').document()
            doc_ref.set(domain_data)
            
            logger.info(f"Domain created with ID: {doc_ref.id}")
            return doc_ref.id
            
        except Exception as e:
            logger.error(f"Error creating domain: {e}")
            raise
    
    def get_domain(self, domain_id: str) -> Optional[Dict[str, Any]]:
        """Get domain by ID."""
        try:
            doc = self.db.collection('domains').document(domain_id).get()
            if doc.exists:
                data = doc.to_dict()
                data['id'] = doc.id
                return data
            return None
            
        except Exception as e:
            logger.error(f"Error getting domain {domain_id}: {e}")
            raise
    
    def get_domains_by_user(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all domains for a user."""
        try:
            query = self.db.collection('domains').where('owner_id', '==', user_id)
            docs = query.stream()
            
            domains = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                domains.append(data)
            
            return domains
            
        except Exception as e:
            logger.error(f"Error getting domains for user {user_id}: {e}")
            raise
    
    def update_domain_status(self, domain_id: str, status: str) -> bool:
        """Update domain status."""
        try:
            update_data = {
                'status': status,
                'updated_at': datetime.utcnow()
            }
            
            self.db.collection('domains').document(domain_id).update(update_data)
            logger.info(f"Domain {domain_id} status updated to {status}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating domain {domain_id} status: {e}")
            return False
    
    # Email Account Management
    def create_email_account(self, account_data: Dict[str, Any]) -> str:
        """Create a new email account."""
        if self.db is None:
            mock_id = f"mock-email-{hash(account_data.get('email', 'test'))}"
            return mock_id

        try:
            account_data['created_at'] = datetime.utcnow()
            account_data['updated_at'] = datetime.utcnow()

            doc_ref = self.db.collection('email_accounts').document()
            doc_ref.set(account_data)

            logger.info(f"Email account created with ID: {doc_ref.id}")
            return doc_ref.id

        except Exception as e:
            logger.error(f"Error creating email account: {e}")
            raise
    
    def get_email_accounts_by_domain(self, domain_id: str) -> List[Dict[str, Any]]:
        """Get all email accounts for a domain."""
        if self.db is None:
            return []  # Mock: no accounts

        try:
            query = self.db.collection('email_accounts').where('domain_id', '==', domain_id)
            docs = query.stream()

            accounts = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                accounts.append(data)

            return accounts

        except Exception as e:
            logger.error(f"Error getting email accounts for domain {domain_id}: {e}")
            raise
    
    def get_email_account_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get email account by email address."""
        if self.db is None:
            return None  # Mock: no existing accounts

        try:
            query = self.db.collection('email_accounts').where('email', '==', email).limit(1)
            docs = query.stream()

            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                return data

            return None

        except Exception as e:
            logger.error(f"Error getting email account {email}: {e}")
            raise

    def get_email_account(self, account_id: str) -> Optional[Dict[str, Any]]:
        """Get email account by ID."""
        if self.db is None:
            return {
                "id": account_id,
                "email": "<EMAIL>",
                "domain_id": "mock-domain",
                "owner_id": "mock-user",
                "status": "active",
                "account_type": "regular",
                "quota_mb": 1024,
                "used_storage_mb": 0
            }

        try:
            doc = self.db.collection('email_accounts').document(account_id).get()
            if doc.exists:
                data = doc.to_dict()
                data['id'] = doc.id
                return data
            return None

        except Exception as e:
            logger.error(f"Error getting email account {account_id}: {e}")
            raise

    def update_email_account(self, account_id: str, update_data: Dict[str, Any]) -> bool:
        """Update email account."""
        if self.db is None:
            return True  # Mock: always successful

        try:
            update_data['updated_at'] = datetime.utcnow()
            self.db.collection('email_accounts').document(account_id).update(update_data)
            logger.info(f"Email account {account_id} updated")
            return True

        except Exception as e:
            logger.error(f"Error updating email account {account_id}: {e}")
            raise

    def delete_email_account(self, account_id: str) -> bool:
        """Delete email account."""
        if self.db is None:
            return True  # Mock: always successful

        try:
            self.db.collection('email_accounts').document(account_id).delete()
            logger.info(f"Email account {account_id} deleted")
            return True

        except Exception as e:
            logger.error(f"Error deleting email account {account_id}: {e}")
            raise
    
    # Analytics and Monitoring
    def log_email_activity(self, activity_data: Dict[str, Any]) -> str:
        """Log email activity for monitoring."""
        try:
            activity_data['timestamp'] = datetime.utcnow()
            
            doc_ref = self.db.collection('email_logs').document()
            doc_ref.set(activity_data)
            
            return doc_ref.id
            
        except Exception as e:
            logger.error(f"Error logging email activity: {e}")
            raise
    
    def get_usage_stats(self, user_id: str, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get usage statistics for a user."""
        try:
            query = (self.db.collection('email_logs')
                    .where('user_id', '==', user_id)
                    .where('timestamp', '>=', start_date)
                    .where('timestamp', '<=', end_date))
            
            docs = query.stream()
            
            stats = {
                'emails_sent': 0,
                'emails_received': 0,
                'storage_used': 0,
                'activities': []
            }
            
            for doc in docs:
                data = doc.to_dict()
                if data.get('action') == 'send':
                    stats['emails_sent'] += 1
                elif data.get('action') == 'receive':
                    stats['emails_received'] += 1
                
                stats['activities'].append(data)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting usage stats for user {user_id}: {e}")
            raise


# Global instance
firestore_service = FirestoreService()
