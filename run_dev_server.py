#!/usr/bin/env python
"""
Development server runner for Email Service.
This script sets up and runs the Django development server.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Run the development server."""
    print("🚀 Starting Email Service Development Server")
    print("=" * 50)
    
    # Set up paths
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    # Check if backend directory exists
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return 1
    
    # Change to backend directory
    os.chdir(backend_dir)
    
    # Set Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'emailservice.settings')
    
    print("📋 Checking setup...")
    
    # Check if .env file exists
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  .env file not found. Copying from .env.example...")
        example_file = project_root / ".env.example"
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            print("✅ .env file created. Please edit it with your settings.")
        else:
            print("❌ .env.example file not found!")
            return 1
    
    # Check if Firebase key exists
    firebase_key = project_root / "custom-domain-email-service-firebase-adminsdk-fbsvc-bb5cfe4813.json"
    if not firebase_key.exists():
        print("⚠️  Firebase service account key not found!")
        print("   Please download it from Firebase Console and save as:")
        print(f"   {firebase_key}")
        print("   Continuing anyway for testing...")
    
    print("🔧 Installing dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "../requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Dependencies installed")
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Warning: Could not install dependencies: {e}")
        print("   Make sure you have activated your virtual environment")
    
    print("🧪 Testing setup...")
    try:
        # Test Django setup
        result = subprocess.run([sys.executable, "manage.py", "check"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Django setup OK")
        else:
            print(f"⚠️  Django check warnings: {result.stdout}")
    except Exception as e:
        print(f"⚠️  Could not run Django check: {e}")
    
    print("\n🌐 Starting development server...")
    print("   API will be available at: http://localhost:8000")
    print("   Health check: http://localhost:8000/health/")
    print("   Admin: http://localhost:8000/admin/")
    print("\n📚 API Endpoints:")
    print("   POST /api/v1/accounts/register/ - Register user")
    print("   POST /api/v1/accounts/login/ - Login user")
    print("   GET  /api/v1/accounts/profile/ - Get profile")
    print("   GET  /api/v1/domains/ - List domains")
    print("   POST /api/v1/domains/ - Create domain")
    print("\n🛑 Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Run Django development server
        subprocess.run([sys.executable, "manage.py", "runserver", "0.0.0.0:8000"])
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
