version: '3.8'

services:
  # Database Services
  postgres:
    image: postgres:15
    container_name: email_postgres
    environment:
      POSTGRES_DB: emailservice
      POSTGRES_USER: emailuser
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-changeme123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - email_network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: email_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-changeme123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - email_network
    restart: unless-stopped

  # Email Server Stack
  postfix:
    build: ./docker/postfix
    container_name: email_postfix
    hostname: mail.${DOMAIN:-example.com}
    environment:
      - DOMAIN=${DOMAIN:-example.com}
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=emailservice
      - POSTGRES_USER=emailuser
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-changeme123}
    volumes:
      - mail_data:/var/mail
      - ./config/postfix:/etc/postfix/custom
      - ./ssl:/etc/ssl/custom
    ports:
      - "25:25"     # SMTP
      - "587:587"   # SMTP Submission
      - "465:465"   # SMTPS
    networks:
      - email_network
    depends_on:
      - postgres
    restart: unless-stopped

  dovecot:
    build: ./docker/dovecot
    container_name: email_dovecot
    hostname: mail.${DOMAIN:-example.com}
    environment:
      - DOMAIN=${DOMAIN:-example.com}
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=emailservice
      - POSTGRES_USER=emailuser
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-changeme123}
    volumes:
      - mail_data:/var/mail
      - ./config/dovecot:/etc/dovecot/custom
      - ./ssl:/etc/ssl/custom
    ports:
      - "143:143"   # IMAP
      - "993:993"   # IMAPS
      - "110:110"   # POP3
      - "995:995"   # POP3S
    networks:
      - email_network
    depends_on:
      - postgres
      - postfix
    restart: unless-stopped

  # Web Applications
  admin_panel:
    build: ./backend
    container_name: email_admin
    environment:
      - DEBUG=${DEBUG:-False}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URL=postgresql://emailuser:${POSTGRES_PASSWORD:-changeme123}@postgres:5432/emailservice
      - REDIS_URL=redis://:${REDIS_PASSWORD:-changeme123}@redis:6379/0
      - DOMAIN=${DOMAIN:-example.com}
    volumes:
      - ./backend:/app
      - mail_data:/var/mail
    ports:
      - "8000:8000"
    networks:
      - email_network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: python manage.py runserver 0.0.0.0:8000

  webmail:
    build: ./frontend
    container_name: email_webmail
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_DOMAIN=${DOMAIN:-example.com}
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    networks:
      - email_network
    depends_on:
      - admin_panel
    restart: unless-stopped

  # Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: email_nginx
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/sites:/etc/nginx/sites-available
      - ./ssl:/etc/ssl/custom
    ports:
      - "80:80"
      - "443:443"
    networks:
      - email_network
    depends_on:
      - admin_panel
      - webmail
    restart: unless-stopped

  # Security & Monitoring
  spamassassin:
    image: instantlinux/spamassassin:latest
    container_name: email_spamassassin
    environment:
      - SPAMD_ARGS=--max-children 5 --helper-home-dir
    volumes:
      - spamassassin_data:/var/lib/spamassassin
    ports:
      - "783:783"
    networks:
      - email_network
    restart: unless-stopped

  clamav:
    image: clamav/clamav:latest
    container_name: email_clamav
    volumes:
      - clamav_data:/var/lib/clamav
    ports:
      - "3310:3310"
    networks:
      - email_network
    restart: unless-stopped

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: email_prometheus
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - email_network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: email_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    networks:
      - email_network
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  mail_data:
  spamassassin_data:
  clamav_data:
  prometheus_data:
  grafana_data:

networks:
  email_network:
    driver: bridge
