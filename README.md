# 🚀 Custom Domain Email Service

A professional email hosting platform that allows businesses to use their own domain for email services. Built with Python, Django, and Google Firestore for scalability and reliability.

## ✨ Current Status: Phase 2 Complete - Domain Management System

We've successfully completed the core backend infrastructure and domain management system. The platform now supports user registration, authentication, and complete domain management with DNS verification.

## 🏗️ Architecture

- **Backend**: Django REST Framework + Google Firestore
- **Frontend**: React + TypeScript (Coming soon)
- **Email Server**: Postfix + Dovecot
- **Database**: Google Firestore (NoSQL, real-time)
- **Cache**: Redis
- **Containerization**: Docker + Docker Compose
- **Monitoring**: Prometheus + Grafana

## 🚀 Quick Start

### Prerequisites

- Docker Desktop installed
- Google Cloud/Firebase project set up
- Domain name for testing (optional for development)

### 1. Clone and Setup

```bash
git clone <repository-url>
cd Custom-Domain-Email-Service
cp .env.example .env
```

### 2. Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or use existing one
3. Enable Firestore Database
4. Go to Project Settings > Service Accounts
5. Generate new private key
6. Save as `firebase-key.json` in project root

### 3. Environment Configuration

Edit `.env` file:

```bash
# Your domain
DOMAIN=yourdomain.com
MAIL_HOSTNAME=mail.yourdomain.com

# Firebase project ID
FIRESTORE_PROJECT_ID=your-firebase-project-id

# Django secret key (generate a secure one)
SECRET_KEY=your-super-secret-key-here

# Other settings...
```

### 4. Build and Run

```bash
# Test setup first
cd backend
python test_setup.py

# Build and start all services
docker-compose up --build
```

### 5. Access Services

- **Admin API**: http://localhost:8000
- **Health Check**: http://localhost:8000/health/
- **Grafana**: http://localhost:3001 (admin/admin123)
- **Prometheus**: http://localhost:9090

## 📁 Project Structure

```
Custom-Domain-Email-Service/
├── backend/                 # Django REST API
│   ├── emailservice/       # Main Django project
│   │   ├── settings.py     # Django configuration
│   │   ├── firestore.py    # Firestore service layer
│   │   └── urls.py         # URL routing
│   ├── accounts/           # User management app
│   ├── domains/            # Domain management app
│   ├── emails/             # Email processing app
│   ├── monitoring/         # Analytics app
│   └── requirements.txt    # Python dependencies
├── frontend/               # React frontend (coming soon)
├── docker/                 # Docker configurations
│   ├── postfix/           # Postfix email server
│   └── dovecot/           # Dovecot IMAP server
├── config/                 # Service configurations
│   ├── nginx/             # Reverse proxy config
│   ├── prometheus/        # Monitoring config
│   └── grafana/           # Dashboard config
├── docker-compose.yml      # Container orchestration
├── .env.example           # Environment template
└── PROJECT_PROPOSAL.md    # Detailed project documentation
```

## 🔥 Firestore Database Schema

```python
# Collections Structure
users/
├── {user_id}/
│   ├── email: string
│   ├── first_name: string
│   ├── last_name: string
│   ├── company_name: string
│   ├── plan: string (free|basic|professional|enterprise)
│   ├── status: string (active|suspended|pending)
│   ├── domains: array
│   ├── created_at: timestamp
│   └── updated_at: timestamp

domains/
├── {domain_id}/
│   ├── name: string
│   ├── owner_id: string
│   ├── status: string (pending|active|suspended)
│   ├── mx_records: array
│   ├── created_at: timestamp
│   └── updated_at: timestamp

email_accounts/
├── {account_id}/
│   ├── email: string
│   ├── domain_id: string
│   ├── password_hash: string
│   ├── quota: number
│   ├── status: string
│   ├── created_at: timestamp
│   └── updated_at: timestamp

email_logs/
├── {log_id}/
│   ├── user_id: string
│   ├── action: string (send|receive|login)
│   ├── timestamp: timestamp
│   ├── details: object
│   └── ip_address: string
```

## 🔧 API Endpoints

### Authentication
- `POST /api/v1/accounts/register/` - User registration
- `POST /api/v1/accounts/login/` - User login
- `GET /api/v1/accounts/profile/` - Get user profile
- `PUT /api/v1/accounts/profile/update/` - Update profile

### Domain Management
- `GET /api/v1/domains/` - List user domains
- `POST /api/v1/domains/` - Add new domain
- `GET /api/v1/domains/{id}/` - Get domain details
- `PUT /api/v1/domains/{id}/` - Update domain
- `DELETE /api/v1/domains/{id}/` - Remove domain

### Email Accounts
- `GET /api/v1/emails/accounts/` - List email accounts
- `POST /api/v1/emails/accounts/` - Create email account
- `PUT /api/v1/emails/accounts/{id}/` - Update email account
- `DELETE /api/v1/emails/accounts/{id}/` - Delete email account

## 🧪 Testing

```bash
# Run backend tests
cd backend
python manage.py test

# Test Firestore connection
python test_setup.py

# Test API endpoints
curl http://localhost:8000/health/
```

## 🚀 Development Workflow

1. **Setup Development Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r backend/requirements.txt
   ```

2. **Run Development Server**
   ```bash
   cd backend
   python manage.py runserver
   ```

3. **Code Quality**
   ```bash
   # Format code
   black backend/
   
   # Lint code
   flake8 backend/
   
   # Type checking
   mypy backend/
   ```

## 📊 Monitoring

- **Prometheus**: Metrics collection at `:9090`
- **Grafana**: Dashboards at `:3001`
- **Health Checks**: Built-in endpoint at `/health/`
- **Logging**: Structured logging with Django

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- CORS protection
- Rate limiting (coming soon)
- SSL/TLS encryption
- Email authentication (SPF, DKIM, DMARC)

## 🌟 Features

### Current (Phase 1)
- ✅ User registration and authentication
- ✅ Firestore integration
- ✅ Docker containerization
- ✅ Health monitoring
- ✅ API documentation

### Coming Soon (Phase 2)
- 🔄 Domain management
- 🔄 Email account creation
- 🔄 Postfix/Dovecot integration
- 🔄 React frontend
- 🔄 Real-time dashboard

### Future (Phase 3)
- 📅 Billing system
- 📅 Advanced monitoring
- 📅 Mobile app
- 📅 API rate limiting
- 📅 Advanced security

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: GitHub Issues
- 📖 Documentation: PROJECT_PROPOSAL.md

---

**Built with ❤️ for affordable email hosting**
