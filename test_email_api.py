#!/usr/bin/env python
"""
Email Account API Testing Script.
Tests the new email account management endpoints.
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class EmailAccountAPITester:
    """Test suite for Email Account API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.token = None
        self.user_id = None
        self.domain_id = None
        self.email_account_id = None
        
    def print_test(self, test_name: str, success: bool, details: str = ""):
        """Print test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
    
    def make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None, 
                    auth: bool = False) -> tuple[bool, Dict[str, Any]]:
        """Make HTTP request to API."""
        url = f"{self.base_url}{endpoint}"
        headers = {'Content-Type': 'application/json'}
        
        if auth and self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=headers)
            elif method == 'PUT':
                response = requests.put(url, json=data, headers=headers)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers)
            else:
                return False, {'error': f'Unsupported method: {method}'}
            
            return response.status_code < 400, response.json()
            
        except requests.exceptions.RequestException as e:
            return False, {'error': f'Request failed: {str(e)}'}
        except json.JSONDecodeError:
            return False, {'error': 'Invalid JSON response'}
    
    def test_health_check(self):
        """Test health endpoint."""
        success, response = self.make_request('GET', '/health/')
        self.print_test("Health Check", success, 
                       f"Status: {response.get('status', 'unknown')}")
        return success
    
    def test_user_registration(self):
        """Test user registration."""
        user_data = {
            'username': f'testuser_{int(time.time())}',
            'email': f'test_{int(time.time())}@example.com',
            'password': 'testpass123'
        }
        
        success, response = self.make_request('POST', '/auth/register/', user_data)
        if success:
            self.user_id = response.get('user', {}).get('id')
        
        self.print_test("User Registration", success, 
                       f"User ID: {self.user_id}")
        return success
    
    def test_user_login(self):
        """Test user login."""
        login_data = {
            'username': f'testuser_{int(time.time())}',
            'password': 'testpass123'
        }
        
        # First register the user
        self.test_user_registration()
        
        success, response = self.make_request('POST', '/auth/login/', login_data)
        if success:
            self.token = response.get('token')
            self.user_id = response.get('user', {}).get('id')
        
        self.print_test("User Login", success, 
                       f"Token: {'✓' if self.token else '✗'}")
        return success
    
    def test_domain_creation(self):
        """Test domain creation."""
        domain_data = {
            'name': 'emailservice.local',
            'description': 'Test domain for email service'
        }
        
        success, response = self.make_request('POST', '/domains/', domain_data, auth=True)
        if success:
            self.domain_id = response.get('domain', {}).get('id')
        
        self.print_test("Domain Creation", success, 
                       f"Domain ID: {self.domain_id}")
        return success
    
    def test_email_account_creation(self):
        """Test email account creation."""
        if not self.domain_id:
            self.print_test("Email Account Creation", False, "No domain available")
            return False
        
        account_data = {
            'email': '<EMAIL>',
            'domain_id': self.domain_id,
            'password': 'emailpass123',
            'quota_mb': 2048,
            'account_type': 'regular'
        }
        
        success, response = self.make_request('POST', '/emails/accounts/', account_data, auth=True)
        if success:
            self.email_account_id = response.get('account', {}).get('id')
        
        self.print_test("Email Account Creation", success, 
                       f"Account ID: {self.email_account_id}")
        return success
    
    def test_email_account_list(self):
        """Test email account listing."""
        success, response = self.make_request('GET', '/emails/accounts/', auth=True)
        
        accounts = response.get('email_accounts', [])
        total = response.get('total', 0)
        
        self.print_test("Email Account List", success, 
                       f"Found {total} accounts")
        return success
    
    def test_email_account_detail(self):
        """Test email account detail retrieval."""
        if not self.email_account_id:
            self.print_test("Email Account Detail", False, "No account available")
            return False
        
        success, response = self.make_request('GET', f'/emails/accounts/{self.email_account_id}/', auth=True)
        
        account = response.get('account', {})
        email = account.get('email', '')
        
        self.print_test("Email Account Detail", success, 
                       f"Email: {email}")
        return success
    
    def test_email_account_update(self):
        """Test email account update."""
        if not self.email_account_id:
            self.print_test("Email Account Update", False, "No account available")
            return False
        
        update_data = {
            'quota_mb': 4096,
            'auto_reply_enabled': True,
            'auto_reply_message': 'Thank you for your email. I will respond soon.'
        }
        
        success, response = self.make_request('PUT', f'/emails/accounts/{self.email_account_id}/', 
                                            update_data, auth=True)
        
        updated_fields = response.get('updated_fields', [])
        
        self.print_test("Email Account Update", success, 
                       f"Updated fields: {', '.join(updated_fields)}")
        return success
    
    def test_email_account_password_change(self):
        """Test email account password change."""
        if not self.email_account_id:
            self.print_test("Email Account Password Change", False, "No account available")
            return False
        
        password_data = {
            'password': 'newemailpass456'
        }
        
        success, response = self.make_request('PUT', f'/emails/accounts/{self.email_account_id}/', 
                                            password_data, auth=True)
        
        self.print_test("Email Account Password Change", success, 
                       "Password updated successfully" if success else response.get('error', ''))
        return success
    
    def test_email_account_deletion(self):
        """Test email account deletion."""
        if not self.email_account_id:
            self.print_test("Email Account Deletion", False, "No account available")
            return False
        
        success, response = self.make_request('DELETE', f'/emails/accounts/{self.email_account_id}/', auth=True)
        
        self.print_test("Email Account Deletion", success, 
                       "Account deleted successfully" if success else response.get('error', ''))
        return success
    
    def run_all_tests(self):
        """Run all email account API tests."""
        print("🧪 Starting Email Account API Tests")
        print("=" * 50)
        
        tests = [
            self.test_health_check,
            self.test_user_login,
            self.test_domain_creation,
            self.test_email_account_creation,
            self.test_email_account_list,
            self.test_email_account_detail,
            self.test_email_account_update,
            self.test_email_account_password_change,
            self.test_email_account_deletion,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            print()  # Empty line between tests
        
        print("=" * 50)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed!")
        else:
            print(f"⚠️  {total - passed} tests failed")
        
        return passed == total


if __name__ == "__main__":
    tester = EmailAccountAPITester()
    tester.run_all_tests()
