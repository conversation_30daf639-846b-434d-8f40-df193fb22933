"""
Email account management views.
"""

import json
import hashlib
import secrets
from datetime import datetime
from typing import Dict, Any, Optional

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View

from accounts.decorators import jwt_required
from emailservice.firestore import FirestoreService
from domains.models import Domain
from .models import EmailAccount, EmailAccountStatus, EmailAccountType, EmailAlias


class EmailAccountListView(View):
    """Handle email account listing and creation."""
    
    @method_decorator(csrf_exempt)
    @method_decorator(jwt_required)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request):
        """List email accounts for user's domains."""
        try:
            user_id = request.user_id
            firestore = FirestoreService()
            
            # Get user's domains first
            domains = firestore.get_domains_by_user(user_id)
            if not domains:
                return JsonResponse({
                    'email_accounts': [],
                    'total': 0,
                    'message': 'No domains found'
                })
            
            # Get email accounts for all user's domains
            all_accounts = []
            for domain in domains:
                accounts = firestore.get_email_accounts_by_domain(domain['id'])
                all_accounts.extend(accounts)
            
            # Format response
            formatted_accounts = []
            for account_data in all_accounts:
                account = EmailAccount.from_dict(account_data, account_data.get('id'))
                formatted_accounts.append({
                    'id': account.id,
                    'email': account.email,
                    'domain_id': account.domain_id,
                    'account_type': account.account_type.value,
                    'status': account.status.value,
                    'quota_mb': account.quota_mb,
                    'used_storage_mb': account.used_storage_mb,
                    'storage_usage_percent': account.storage_usage_percent,
                    'is_active': account.is_active,
                    'can_send_email': account.can_send_email(),
                    'can_receive_email': account.can_receive_email(),
                    'created_at': account.created_at.isoformat() if account.created_at else None,
                    'last_login': account.last_login.isoformat() if account.last_login else None,
                })
            
            return JsonResponse({
                'email_accounts': formatted_accounts,
                'total': len(formatted_accounts),
                'domains_count': len(domains)
            })
            
        except Exception as e:
            return JsonResponse({
                'error': f'Failed to retrieve email accounts: {str(e)}'
            }, status=500)
    
    def post(self, request):
        """Create new email account."""
        try:
            user_id = request.user_id
            data = json.loads(request.body)
            
            # Validate required fields
            required_fields = ['email', 'domain_id', 'password']
            for field in required_fields:
                if field not in data:
                    return JsonResponse({
                        'error': f'Missing required field: {field}'
                    }, status=400)
            
            email = data['email'].lower().strip()
            domain_id = data['domain_id']
            password = data['password']
            
            # Validate email format
            if not EmailAccount.is_valid_email(email):
                return JsonResponse({
                    'error': 'Invalid email format'
                }, status=400)
            
            # Verify domain ownership
            firestore = FirestoreService()
            domain_data = firestore.get_domain(domain_id)
            if not domain_data or domain_data.get('owner_id') != user_id:
                return JsonResponse({
                    'error': 'Domain not found or access denied'
                }, status=403)
            
            domain = Domain.from_dict(domain_data, domain_id)
            
            # Verify email domain matches
            email_domain = email.split('@')[1]
            if email_domain != domain.name:
                return JsonResponse({
                    'error': f'Email domain {email_domain} does not match domain {domain.name}'
                }, status=400)
            
            # Check if email already exists
            existing_account = firestore.get_email_account_by_email(email)
            if existing_account:
                return JsonResponse({
                    'error': 'Email account already exists'
                }, status=409)
            
            # Hash password
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # Create email account
            account = EmailAccount(
                email=email,
                domain_id=domain_id,
                owner_id=user_id,
                account_type=EmailAccountType(data.get('account_type', 'regular')),
                status=EmailAccountStatus.ACTIVE,
                password_hash=password_hash,
                quota_mb=data.get('quota_mb', 1024),
                forward_to=data.get('forward_to'),
                auto_reply_enabled=data.get('auto_reply_enabled', False),
                auto_reply_message=data.get('auto_reply_message'),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Save to Firestore
            account_id = firestore.create_email_account(account.to_dict())
            account.id = account_id
            
            return JsonResponse({
                'message': 'Email account created successfully',
                'account': {
                    'id': account.id,
                    'email': account.email,
                    'domain_id': account.domain_id,
                    'account_type': account.account_type.value,
                    'status': account.status.value,
                    'quota_mb': account.quota_mb,
                    'created_at': account.created_at.isoformat()
                }
            }, status=201)
            
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            return JsonResponse({
                'error': f'Failed to create email account: {str(e)}'
            }, status=500)


class EmailAccountDetailView(View):
    """Handle individual email account operations."""
    
    @method_decorator(csrf_exempt)
    @method_decorator(jwt_required)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request, account_id):
        """Get email account details."""
        try:
            user_id = request.user_id
            firestore = FirestoreService()
            
            # Get account
            account_data = firestore.get_email_account(account_id)
            if not account_data:
                return JsonResponse({'error': 'Email account not found'}, status=404)
            
            # Verify ownership through domain
            domain_data = firestore.get_domain(account_data['domain_id'])
            if not domain_data or domain_data.get('owner_id') != user_id:
                return JsonResponse({'error': 'Access denied'}, status=403)
            
            account = EmailAccount.from_dict(account_data, account_id)
            
            return JsonResponse({
                'account': {
                    'id': account.id,
                    'email': account.email,
                    'domain_id': account.domain_id,
                    'account_type': account.account_type.value,
                    'status': account.status.value,
                    'quota_mb': account.quota_mb,
                    'used_storage_mb': account.used_storage_mb,
                    'storage_usage_percent': account.storage_usage_percent,
                    'forward_to': account.forward_to,
                    'auto_reply_enabled': account.auto_reply_enabled,
                    'auto_reply_message': account.auto_reply_message,
                    'is_active': account.is_active,
                    'can_send_email': account.can_send_email(),
                    'can_receive_email': account.can_receive_email(),
                    'created_at': account.created_at.isoformat() if account.created_at else None,
                    'updated_at': account.updated_at.isoformat() if account.updated_at else None,
                    'last_login': account.last_login.isoformat() if account.last_login else None,
                }
            })
            
        except Exception as e:
            return JsonResponse({
                'error': f'Failed to retrieve email account: {str(e)}'
            }, status=500)
    
    def put(self, request, account_id):
        """Update email account."""
        try:
            user_id = request.user_id
            data = json.loads(request.body)
            firestore = FirestoreService()
            
            # Get and verify account
            account_data = firestore.get_email_account(account_id)
            if not account_data:
                return JsonResponse({'error': 'Email account not found'}, status=404)
            
            # Verify ownership through domain
            domain_data = firestore.get_domain(account_data['domain_id'])
            if not domain_data or domain_data.get('owner_id') != user_id:
                return JsonResponse({'error': 'Access denied'}, status=403)
            
            # Update allowed fields
            update_data = {'updated_at': datetime.utcnow()}
            
            if 'status' in data:
                try:
                    update_data['status'] = EmailAccountStatus(data['status']).value
                except ValueError:
                    return JsonResponse({'error': 'Invalid status'}, status=400)
            
            if 'quota_mb' in data:
                quota = int(data['quota_mb'])
                if quota < 0:
                    return JsonResponse({'error': 'Quota must be non-negative'}, status=400)
                update_data['quota_mb'] = quota
            
            if 'forward_to' in data:
                forward_to = data['forward_to']
                if forward_to and not EmailAccount.is_valid_email(forward_to):
                    return JsonResponse({'error': 'Invalid forward_to email'}, status=400)
                update_data['forward_to'] = forward_to
            
            if 'auto_reply_enabled' in data:
                update_data['auto_reply_enabled'] = bool(data['auto_reply_enabled'])
            
            if 'auto_reply_message' in data:
                update_data['auto_reply_message'] = data['auto_reply_message']
            
            if 'password' in data:
                password_hash = hashlib.sha256(data['password'].encode()).hexdigest()
                update_data['password_hash'] = password_hash
            
            # Update in Firestore
            firestore.update_email_account(account_id, update_data)
            
            return JsonResponse({
                'message': 'Email account updated successfully',
                'updated_fields': list(update_data.keys())
            })
            
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except ValueError as e:
            return JsonResponse({'error': str(e)}, status=400)
        except Exception as e:
            return JsonResponse({
                'error': f'Failed to update email account: {str(e)}'
            }, status=500)
    
    def delete(self, request, account_id):
        """Delete email account."""
        try:
            user_id = request.user_id
            firestore = FirestoreService()
            
            # Get and verify account
            account_data = firestore.get_email_account(account_id)
            if not account_data:
                return JsonResponse({'error': 'Email account not found'}, status=404)
            
            # Verify ownership through domain
            domain_data = firestore.get_domain(account_data['domain_id'])
            if not domain_data or domain_data.get('owner_id') != user_id:
                return JsonResponse({'error': 'Access denied'}, status=403)
            
            # Delete from Firestore
            firestore.delete_email_account(account_id)
            
            return JsonResponse({
                'message': 'Email account deleted successfully'
            })
            
        except Exception as e:
            return JsonResponse({
                'error': f'Failed to delete email account: {str(e)}'
            }, status=500)
