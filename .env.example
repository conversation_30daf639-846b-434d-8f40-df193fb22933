# Domain Configuration
DOMAIN=emailservice.local
HOSTNAME=mail.emailservice.local

# Firestore Configuration (Primary Database)
FIRESTORE_PROJECT_ID=custom-domain-email-service
GOOGLE_APPLICATION_CREDENTIALS=./custom-domain-email-service-firebase-adminsdk-fbsvc-bb5cfe4813.json

# Redis Configuration (Caching & Sessions only)
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_URL=redis://localhost:6379/0

# Django Configuration
SECRET_KEY=your_django_secret_key_here_make_it_long_and_random
DEBUG=False

# Email Configuration
POSTMASTER_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/custom/cert.pem
SSL_KEY_PATH=/etc/ssl/custom/private.key

# Monitoring
GRAFANA_PASSWORD=your_grafana_admin_password

# External Services
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# DNS Configuration (for automated DNS management)
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id

# Backup Configuration
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# SMTP Relay (optional, for initial setup)
SMTP_RELAY_HOST=smtp.gmail.com
SMTP_RELAY_PORT=587
SMTP_RELAY_USER=<EMAIL>
SMTP_RELAY_PASSWORD=your_app_password
