# 🚀 Email Service Setup Guide

## Prerequisites for Windows 10

### 1. Install Docker Desktop (Recommended)
1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop/
2. Install and restart your computer
3. Start Docker Desktop
4. Verify installation: `docker --version`

### 2. Alternative: Python Development Setup
If you prefer to run without Docker:

1. **Install Python 3.11+**
   - Download from: https://www.python.org/downloads/
   - Make sure to check "Add Python to PATH"

2. **Install Git** (if not already installed)
   - Download from: https://git-scm.com/download/win

3. **Install Redis** (for caching)
   - Download from: https://github.com/microsoftarchive/redis/releases
   - Or use Redis Cloud: https://redis.com/try-free/

## 🔥 Firebase Setup

### 1. Create Firebase Project
1. Go to https://console.firebase.google.com/
2. Click "Create a project"
3. Project name: `custom-domain-email-service`
4. Enable Google Analytics (optional)
5. Create project

### 2. Enable Firestore
1. In Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location (choose closest to you)

### 3. Get Service Account Key
1. Go to Project Settings (gear icon)
2. Click "Service accounts" tab
3. Click "Generate new private key"
4. Save the JSON file as `custom-domain-email-service-firebase-adminsdk-fbsvc-bb5cfe4813.json`
5. Place it in the project root directory

## 🚀 Quick Start Options

### Option A: Docker Setup (Recommended)

```bash
# 1. Clone and setup
git clone <your-repo-url>
cd Custom-Domain-Email-Service

# 2. Copy environment file
copy .env.example .env

# 3. Edit .env file with your Firebase project ID
# FIRESTORE_PROJECT_ID=custom-domain-email-service

# 4. Build and start all services
docker-compose up --build

# 5. Access the application
# API: http://localhost:8000
# Grafana: http://localhost:3001 (admin/admin123)
```

### Option B: Python Development Setup

```bash
# 1. Clone and setup
git clone <your-repo-url>
cd Custom-Domain-Email-Service

# 2. Create virtual environment
python -m venv venv
venv\Scripts\activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Copy and edit environment
copy .env.example .env
# Edit .env with your settings

# 5. Test setup
cd backend
python test_setup.py

# 6. Run development server
python manage.py runserver
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Domain Configuration
DOMAIN=emailservice.local
HOSTNAME=mail.emailservice.local

# Firebase Configuration
FIRESTORE_PROJECT_ID=custom-domain-email-service
GOOGLE_APPLICATION_CREDENTIALS=./custom-domain-email-service-firebase-adminsdk-fbsvc-bb5cfe4813.json

# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,emailservice.local

# Redis (if running locally)
REDIS_URL=redis://localhost:6379/0
```

## 🧪 Testing the Setup

### 1. Test Backend
```bash
cd backend
python test_setup.py
```

### 2. Test API Endpoints
```bash
# Health check
curl http://localhost:8000/health/

# Register user
curl -X POST http://localhost:8000/api/v1/accounts/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User",
    "password": "testpassword123"
  }'
```

### 3. Test Domain Management
```bash
# Login first to get token
curl -X POST http://localhost:8000/api/v1/accounts/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }'

# Use the token to create a domain
curl -X POST http://localhost:8000/api/v1/domains/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "name": "example.com"
  }'
```

## 🐳 Docker Services

When using Docker, the following services will be available:

| Service | Port | Description |
|---------|------|-------------|
| Django API | 8000 | Main application backend |
| Redis | 6379 | Caching and sessions |
| Postfix | 25, 587 | SMTP email server |
| Dovecot | 143, 993 | IMAP email server |
| Nginx | 80, 443 | Reverse proxy |
| Prometheus | 9090 | Metrics collection |
| Grafana | 3001 | Monitoring dashboards |

## 🔍 Troubleshooting

### Common Issues

1. **Firebase Connection Error**
   - Check if `custom-domain-email-service-firebase-adminsdk-fbsvc-bb5cfe4813.json` exists
   - Verify FIRESTORE_PROJECT_ID in .env matches your Firebase project
   - Ensure Firestore is enabled in Firebase Console

2. **Docker Issues**
   - Make sure Docker Desktop is running
   - Try `docker-compose down` then `docker-compose up --build`
   - Check Docker Desktop logs

3. **Port Conflicts**
   - Make sure ports 8000, 6379, 3001, 9090 are not in use
   - Use `netstat -an | findstr :8000` to check port usage

4. **Python Issues**
   - Make sure you're using Python 3.11+
   - Activate virtual environment: `venv\Scripts\activate`
   - Install dependencies: `pip install -r requirements.txt`

### Getting Help

1. Check the logs:
   ```bash
   # Docker logs
   docker-compose logs backend
   
   # Python logs
   cd backend && python manage.py runserver --verbosity=2
   ```

2. Test individual components:
   ```bash
   # Test Firestore connection
   cd backend && python -c "from emailservice.firestore import firestore_service; print('Firestore OK')"
   
   # Test Django setup
   cd backend && python manage.py check
   ```

## 🎯 Next Steps

After successful setup:

1. **Create your first user account** via API
2. **Add a domain** for email hosting
3. **Configure DNS records** as shown in the domain setup
4. **Test email functionality** with the provided tools
5. **Explore the monitoring dashboard** at http://localhost:3001

## 📚 Additional Resources

- [Django Documentation](https://docs.djangoproject.com/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Docker Documentation](https://docs.docker.com/)
- [Postfix Documentation](http://www.postfix.org/documentation.html)

---

**Need help?** Check the troubleshooting section or create an issue in the repository.
