@echo off
echo 🚀 Starting Email Service Development Server
echo ================================================

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.11+ from https://python.org
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found. Copying from .env.example...
    if exist ".env.example" (
        copy ".env.example" ".env"
        echo ✅ .env file created. Please edit it with your settings.
    ) else (
        echo ❌ .env.example file not found!
        pause
        exit /b 1
    )
)

REM Check if Firebase key exists
if not exist "custom-domain-email-service-firebase-adminsdk-fbsvc-bb5cfe4813.json" (
    echo ⚠️  Firebase service account key not found!
    echo    Please download it from Firebase Console and save as:
    echo    custom-domain-email-service-firebase-adminsdk-fbsvc-bb5cfe4813.json
    echo    Continuing anyway for testing...
)

echo 🔧 Installing dependencies...
pip install -r requirements.txt

echo 🧪 Testing Django setup...
cd backend
python manage.py check

echo 🌐 Starting development server...
echo    API will be available at: http://localhost:8000
echo    Health check: http://localhost:8000/health/
echo    Press Ctrl+C to stop the server
echo ================================================

python manage.py runserver 0.0.0.0:8000

pause
