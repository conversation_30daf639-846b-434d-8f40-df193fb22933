"""
Domain management services for DNS operations and verification.
"""

import logging
import socket
import dns.resolver
import dns.exception
from typing import List, Dict, Any, Optional
from datetime import datetime

from .models import Domain, DNSRecord, DNSRecordType, DomainVerification

logger = logging.getLogger(__name__)


class DNSService:
    """Service for DNS operations and record management."""
    
    def __init__(self, mail_server: str = "mail.emailservice.local"):
        self.mail_server = mail_server
        self.resolver = dns.resolver.Resolver()
        self.resolver.timeout = 10
        self.resolver.lifetime = 30
    
    def get_required_dns_records(self, domain_name: str, verification_token: str) -> List[DNSRecord]:
        """Get all required DNS records for email hosting."""
        records = []
        
        # MX Record
        records.append(DNSRecord(
            record_type=DNSRecordType.MX,
            name=domain_name,
            value=self.mail_server,
            priority=10,
            ttl=3600
        ))
        
        # SPF Record
        records.append(DNSRecord(
            record_type=DNSRecordType.TXT,
            name=domain_name,
            value="v=spf1 mx ~all",
            ttl=3600
        ))
        
        # DMARC Record
        records.append(DNSRecord(
            record_type=DNSRecordType.TXT,
            name=f"_dmarc.{domain_name}",
            value="v=DMARC1; p=quarantine; rua=mailto:<EMAIL>",
            ttl=3600
        ))
        
        # Domain Verification Record
        records.append(DNSRecord(
            record_type=DNSRecordType.TXT,
            name=f"_emailservice-verification.{domain_name}",
            value=f"emailservice-verification={verification_token}",
            ttl=300
        ))
        
        # Optional: DKIM Record (placeholder)
        records.append(DNSRecord(
            record_type=DNSRecordType.TXT,
            name=f"default._domainkey.{domain_name}",
            value="v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC...",
            ttl=3600
        ))
        
        return records
    
    def check_mx_records(self, domain_name: str) -> Dict[str, Any]:
        """Check if MX records are properly configured."""
        try:
            mx_records = self.resolver.resolve(domain_name, 'MX')
            
            found_our_server = False
            mx_list = []
            
            for mx in mx_records:
                mx_host = str(mx.exchange).rstrip('.')
                mx_list.append({
                    'host': mx_host,
                    'priority': mx.preference
                })
                
                if mx_host == self.mail_server:
                    found_our_server = True
            
            return {
                'success': found_our_server,
                'mx_records': mx_list,
                'message': 'MX records configured correctly' if found_our_server else 'Our MX record not found'
            }
            
        except dns.resolver.NXDOMAIN:
            return {
                'success': False,
                'mx_records': [],
                'message': 'Domain does not exist'
            }
        except dns.resolver.NoAnswer:
            return {
                'success': False,
                'mx_records': [],
                'message': 'No MX records found'
            }
        except Exception as e:
            logger.error(f"Error checking MX records for {domain_name}: {e}")
            return {
                'success': False,
                'mx_records': [],
                'message': f'DNS lookup failed: {str(e)}'
            }
    
    def check_txt_record(self, domain_name: str, expected_value: str) -> Dict[str, Any]:
        """Check if specific TXT record exists."""
        try:
            txt_records = self.resolver.resolve(domain_name, 'TXT')
            
            found_record = False
            txt_list = []
            
            for txt in txt_records:
                txt_value = ''.join([part.decode() if isinstance(part, bytes) else str(part) for part in txt.strings])
                txt_list.append(txt_value)
                
                if expected_value in txt_value:
                    found_record = True
            
            return {
                'success': found_record,
                'txt_records': txt_list,
                'message': 'TXT record found' if found_record else 'Required TXT record not found'
            }
            
        except dns.resolver.NXDOMAIN:
            return {
                'success': False,
                'txt_records': [],
                'message': 'Domain does not exist'
            }
        except dns.resolver.NoAnswer:
            return {
                'success': False,
                'txt_records': [],
                'message': 'No TXT records found'
            }
        except Exception as e:
            logger.error(f"Error checking TXT record for {domain_name}: {e}")
            return {
                'success': False,
                'txt_records': [],
                'message': f'DNS lookup failed: {str(e)}'
            }
    
    def check_spf_record(self, domain_name: str) -> Dict[str, Any]:
        """Check SPF record configuration."""
        expected_spf = "v=spf1 mx ~all"
        result = self.check_txt_record(domain_name, "v=spf1")
        
        if result['success']:
            # Check if it includes our MX servers
            for txt_record in result['txt_records']:
                if 'v=spf1' in txt_record and 'mx' in txt_record:
                    result['message'] = 'SPF record configured correctly'
                    return result
            
            result['success'] = False
            result['message'] = 'SPF record found but may not include MX servers'
        
        return result
    
    def check_dmarc_record(self, domain_name: str) -> Dict[str, Any]:
        """Check DMARC record configuration."""
        dmarc_domain = f"_dmarc.{domain_name}"
        return self.check_txt_record(dmarc_domain, "v=DMARC1")


class DomainService:
    """Service for domain management and verification."""
    
    def __init__(self):
        self.dns_service = DNSService()
    
    def verify_domain(self, domain: Domain) -> Dict[str, Any]:
        """Verify domain ownership and configuration."""
        verification_results = {
            'success': False,
            'checks': {},
            'error': None
        }
        
        try:
            # Check domain verification token
            verification_domain = f"_emailservice-verification.{domain.name}"
            expected_token = f"emailservice-verification={domain.verification_token}"
            
            token_check = self.dns_service.check_txt_record(verification_domain, expected_token)
            verification_results['checks']['verification_token'] = token_check
            
            if not token_check['success']:
                verification_results['error'] = 'Domain verification token not found in DNS'
                return verification_results
            
            # Check MX records
            mx_check = self.dns_service.check_mx_records(domain.name)
            verification_results['checks']['mx_records'] = mx_check
            
            # Check SPF record
            spf_check = self.dns_service.check_spf_record(domain.name)
            verification_results['checks']['spf_record'] = spf_check
            
            # Check DMARC record (optional for verification)
            dmarc_check = self.dns_service.check_dmarc_record(domain.name)
            verification_results['checks']['dmarc_record'] = dmarc_check
            
            # Domain is verified if token and MX records are correct
            if token_check['success'] and mx_check['success']:
                verification_results['success'] = True
                verification_results['message'] = 'Domain verified successfully'
            else:
                verification_results['error'] = 'Required DNS records not properly configured'
            
            return verification_results
            
        except Exception as e:
            logger.error(f"Error verifying domain {domain.name}: {e}")
            verification_results['error'] = f'Verification failed: {str(e)}'
            return verification_results
    
    def get_domain_health_status(self, domain: Domain) -> Dict[str, Any]:
        """Get comprehensive health status of domain configuration."""
        health_status = {
            'overall_status': 'unknown',
            'checks': {},
            'recommendations': []
        }
        
        try:
            # Check all DNS records
            mx_check = self.dns_service.check_mx_records(domain.name)
            spf_check = self.dns_service.check_spf_record(domain.name)
            dmarc_check = self.dns_service.check_dmarc_record(domain.name)
            
            health_status['checks'] = {
                'mx_records': mx_check,
                'spf_record': spf_check,
                'dmarc_record': dmarc_check,
            }
            
            # Determine overall status
            critical_checks = [mx_check['success']]
            important_checks = [spf_check['success']]
            optional_checks = [dmarc_check['success']]
            
            if all(critical_checks):
                if all(important_checks):
                    if all(optional_checks):
                        health_status['overall_status'] = 'excellent'
                    else:
                        health_status['overall_status'] = 'good'
                        health_status['recommendations'].append('Configure DMARC record for better email security')
                else:
                    health_status['overall_status'] = 'warning'
                    health_status['recommendations'].append('Configure SPF record to prevent email spoofing')
            else:
                health_status['overall_status'] = 'critical'
                health_status['recommendations'].append('Configure MX records to receive emails')
            
            # Add specific recommendations
            if not mx_check['success']:
                health_status['recommendations'].append('Add MX record pointing to mail.emailservice.local')
            
            if not spf_check['success']:
                health_status['recommendations'].append('Add SPF TXT record: v=spf1 mx ~all')
            
            if not dmarc_check['success']:
                health_status['recommendations'].append('Add DMARC TXT record for email policy')
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error getting domain health for {domain.name}: {e}")
            health_status['overall_status'] = 'error'
            health_status['error'] = str(e)
            return health_status
    
    def generate_dns_instructions(self, domain: Domain) -> Dict[str, Any]:
        """Generate step-by-step DNS setup instructions."""
        required_records = self.dns_service.get_required_dns_records(domain.name, domain.verification_token)
        
        instructions = {
            'domain': domain.name,
            'steps': [],
            'records': []
        }
        
        # Add step-by-step instructions
        instructions['steps'] = [
            {
                'step': 1,
                'title': 'Access your domain DNS settings',
                'description': 'Log into your domain registrar or DNS provider (e.g., GoDaddy, Namecheap, Cloudflare)'
            },
            {
                'step': 2,
                'title': 'Add MX record',
                'description': 'Add the MX record to route emails to our servers'
            },
            {
                'step': 3,
                'title': 'Add TXT records',
                'description': 'Add the TXT records for verification and email authentication'
            },
            {
                'step': 4,
                'title': 'Wait for DNS propagation',
                'description': 'DNS changes can take up to 24 hours to propagate worldwide'
            },
            {
                'step': 5,
                'title': 'Verify domain',
                'description': 'Click the verify button to check your DNS configuration'
            }
        ]
        
        # Add record details
        for record in required_records:
            record_info = {
                'type': record.record_type.value,
                'name': record.name,
                'value': record.value,
                'ttl': record.ttl,
                'description': self._get_record_description(record)
            }
            
            if record.priority is not None:
                record_info['priority'] = record.priority
            
            instructions['records'].append(record_info)
        
        return instructions
    
    def _get_record_description(self, record: DNSRecord) -> str:
        """Get human-readable description for DNS record."""
        descriptions = {
            DNSRecordType.MX: 'Routes emails to our mail servers',
            DNSRecordType.TXT: 'Text record for verification or email authentication',
        }
        
        if record.record_type == DNSRecordType.TXT:
            if 'emailservice-verification' in record.value:
                return 'Domain ownership verification record'
            elif 'v=spf1' in record.value:
                return 'SPF record for email authentication'
            elif 'v=DMARC1' in record.value:
                return 'DMARC record for email policy'
            elif 'v=DKIM1' in record.value:
                return 'DKIM record for email signing'
        
        return descriptions.get(record.record_type, 'DNS record')
