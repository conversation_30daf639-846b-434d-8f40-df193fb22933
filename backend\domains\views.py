"""
Domain management views using Firestore backend.
"""

import logging
import secrets
import string
from datetime import datetime
from typing import Dict, Any

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
import json
import jwt
from django.conf import settings

from emailservice.firestore import firestore_service
from .models import Domain, DomainStatus, DNSRecord, DNSRecordType, DomainVerification
from .services import DomainService, DNSService

logger = logging.getLogger(__name__)


def get_user_from_token(request):
    """Extract user ID from JWT token."""
    auth_header = request.headers.get('Authorization', '')
    if not auth_header.startswith('Bearer '):
        raise ValueError('Invalid authorization header')
    
    token = auth_header.split(' ')[1]
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
    return payload['user_id']


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def domains_list_view(request):
    """List user domains or create new domain."""
    try:
        user_id = get_user_from_token(request)
        
        if request.method == 'GET':
            # Get user's domains
            domains_data = firestore_service.get_domains_by_user(user_id)
            domains = [Domain.from_dict(data, data['id']) for data in domains_data]
            
            return Response({
                'domains': [
                    {
                        'id': domain.id,
                        'name': domain.name,
                        'status': domain.status.value,
                        'email_accounts_count': domain.email_accounts_count,
                        'storage_used_mb': domain.storage_used_mb,
                        'is_verified': domain.is_verified,
                        'created_at': domain.created_at.isoformat() if domain.created_at else None,
                        'verified_at': domain.verified_at.isoformat() if domain.verified_at else None,
                    }
                    for domain in domains
                ],
                'total': len(domains)
            })
        
        elif request.method == 'POST':
            # Create new domain
            domain_name = request.data.get('name', '').lower().strip()
            
            if not domain_name:
                return Response({'error': 'Domain name is required'}, status=400)
            
            if not Domain.is_valid_domain(domain_name):
                return Response({'error': 'Invalid domain name format'}, status=400)
            
            # Check if domain already exists
            existing_domains = firestore_service.get_domains_by_user(user_id)
            for existing in existing_domains:
                if existing['name'] == domain_name:
                    return Response({'error': 'Domain already exists'}, status=400)
            
            # Check user plan limits
            user_data = firestore_service.get_user(user_id)
            if not user_data:
                return Response({'error': 'User not found'}, status=404)
            
            user_plan = user_data.get('plan', 'free')
            if len(existing_domains) >= get_domain_limit(user_plan):
                return Response({
                    'error': f'Domain limit reached for {user_plan} plan'
                }, status=403)
            
            # Generate verification token
            verification_token = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
            
            # Create domain
            domain = Domain(
                name=domain_name,
                owner_id=user_id,
                status=DomainStatus.PENDING,
                verification_token=verification_token,
                verification_method='dns'
            )
            
            domain_id = firestore_service.create_domain(domain.to_dict())
            
            # Create DNS service instance and get required records
            dns_service = DNSService()
            required_records = dns_service.get_required_dns_records(domain_name, verification_token)
            
            logger.info(f"Domain created: {domain_name} for user {user_id}")
            
            return Response({
                'message': 'Domain created successfully',
                'domain': {
                    'id': domain_id,
                    'name': domain_name,
                    'status': domain.status.value,
                    'verification_token': verification_token,
                    'required_dns_records': [record.to_dict() for record in required_records],
                }
            }, status=201)
    
    except jwt.ExpiredSignatureError:
        return Response({'error': 'Token expired'}, status=401)
    except jwt.InvalidTokenError:
        return Response({'error': 'Invalid token'}, status=401)
    except Exception as e:
        logger.error(f"Domains list view error: {e}")
        return Response({'error': 'Operation failed'}, status=500)


@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def domain_detail_view(request, domain_id):
    """Get, update, or delete specific domain."""
    try:
        user_id = get_user_from_token(request)
        
        # Get domain
        domain_data = firestore_service.get_domain(domain_id)
        if not domain_data:
            return Response({'error': 'Domain not found'}, status=404)
        
        domain = Domain.from_dict(domain_data, domain_id)
        
        # Check ownership
        if domain.owner_id != user_id:
            return Response({'error': 'Access denied'}, status=403)
        
        if request.method == 'GET':
            # Get email accounts for this domain
            email_accounts = firestore_service.get_email_accounts_by_domain(domain_id)
            
            # Get DNS records
            dns_service = DNSService()
            required_records = dns_service.get_required_dns_records(domain.name, domain.verification_token)
            
            return Response({
                'domain': {
                    'id': domain.id,
                    'name': domain.name,
                    'status': domain.status.value,
                    'email_accounts_count': len(email_accounts),
                    'storage_used_mb': domain.storage_used_mb,
                    'verification_token': domain.verification_token,
                    'verification_method': domain.verification_method,
                    'is_verified': domain.is_verified,
                    'created_at': domain.created_at.isoformat() if domain.created_at else None,
                    'verified_at': domain.verified_at.isoformat() if domain.verified_at else None,
                    'required_dns_records': [record.to_dict() for record in required_records],
                    'email_accounts': email_accounts,
                }
            })
        
        elif request.method == 'PUT':
            # Update domain (limited fields)
            update_data = {}
            
            # Only allow updating verification method
            if 'verification_method' in request.data:
                method = request.data['verification_method']
                if method in ['dns', 'file', 'email']:
                    update_data['verification_method'] = method
            
            if update_data:
                success = firestore_service.update_domain_status(domain_id, domain.status.value)
                if success:
                    return Response({'message': 'Domain updated successfully'})
                else:
                    return Response({'error': 'Failed to update domain'}, status=500)
            
            return Response({'message': 'No changes made'})
        
        elif request.method == 'DELETE':
            # Check if domain has email accounts
            email_accounts = firestore_service.get_email_accounts_by_domain(domain_id)
            if email_accounts:
                return Response({
                    'error': 'Cannot delete domain with existing email accounts'
                }, status=400)
            
            # Delete domain (in a real implementation, you'd soft delete)
            # For now, we'll just mark as suspended
            success = firestore_service.update_domain_status(domain_id, DomainStatus.SUSPENDED.value)
            if success:
                logger.info(f"Domain deleted: {domain.name} by user {user_id}")
                return Response({'message': 'Domain deleted successfully'})
            else:
                return Response({'error': 'Failed to delete domain'}, status=500)
    
    except jwt.ExpiredSignatureError:
        return Response({'error': 'Token expired'}, status=401)
    except jwt.InvalidTokenError:
        return Response({'error': 'Invalid token'}, status=401)
    except Exception as e:
        logger.error(f"Domain detail view error: {e}")
        return Response({'error': 'Operation failed'}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_domain_view(request, domain_id):
    """Verify domain ownership."""
    try:
        user_id = get_user_from_token(request)
        
        # Get domain
        domain_data = firestore_service.get_domain(domain_id)
        if not domain_data:
            return Response({'error': 'Domain not found'}, status=404)
        
        domain = Domain.from_dict(domain_data, domain_id)
        
        # Check ownership
        if domain.owner_id != user_id:
            return Response({'error': 'Access denied'}, status=403)
        
        # Check if already verified
        if domain.is_verified:
            return Response({'message': 'Domain already verified'})
        
        # Perform verification
        domain_service = DomainService()
        verification_result = domain_service.verify_domain(domain)
        
        if verification_result['success']:
            # Update domain status
            update_data = {
                'status': DomainStatus.ACTIVE.value,
                'verified_at': datetime.utcnow(),
            }
            firestore_service.update_domain_status(domain_id, DomainStatus.ACTIVE.value)
            
            logger.info(f"Domain verified: {domain.name}")
            
            return Response({
                'message': 'Domain verified successfully',
                'verification_details': verification_result
            })
        else:
            return Response({
                'error': 'Domain verification failed',
                'details': verification_result.get('error', 'Unknown error')
            }, status=400)
    
    except jwt.ExpiredSignatureError:
        return Response({'error': 'Token expired'}, status=401)
    except jwt.InvalidTokenError:
        return Response({'error': 'Invalid token'}, status=401)
    except Exception as e:
        logger.error(f"Domain verification error: {e}")
        return Response({'error': 'Verification failed'}, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def domain_dns_records_view(request, domain_id):
    """Get DNS records for domain setup."""
    try:
        user_id = get_user_from_token(request)
        
        # Get domain
        domain_data = firestore_service.get_domain(domain_id)
        if not domain_data:
            return Response({'error': 'Domain not found'}, status=404)
        
        domain = Domain.from_dict(domain_data, domain_id)
        
        # Check ownership
        if domain.owner_id != user_id:
            return Response({'error': 'Access denied'}, status=403)
        
        # Get DNS records
        dns_service = DNSService()
        required_records = dns_service.get_required_dns_records(domain.name, domain.verification_token)
        
        return Response({
            'domain': domain.name,
            'dns_records': [
                {
                    'type': record.record_type.value,
                    'name': record.name,
                    'value': record.value,
                    'priority': record.priority,
                    'ttl': record.ttl,
                    'description': get_dns_record_description(record.record_type)
                }
                for record in required_records
            ],
            'instructions': {
                'mx': 'Add this MX record to route emails to our servers',
                'txt_spf': 'Add this TXT record for SPF email authentication',
                'txt_dmarc': 'Add this TXT record for DMARC email policy',
                'txt_verification': 'Add this TXT record to verify domain ownership',
            }
        })
    
    except jwt.ExpiredSignatureError:
        return Response({'error': 'Token expired'}, status=401)
    except jwt.InvalidTokenError:
        return Response({'error': 'Invalid token'}, status=401)
    except Exception as e:
        logger.error(f"DNS records view error: {e}")
        return Response({'error': 'Failed to get DNS records'}, status=500)


def get_domain_limit(plan: str) -> int:
    """Get domain limit for user plan."""
    limits = {
        'free': 1,
        'basic': 5,
        'professional': 25,
        'enterprise': 100,
    }
    return limits.get(plan, 1)


def get_dns_record_description(record_type: DNSRecordType) -> str:
    """Get description for DNS record type."""
    descriptions = {
        DNSRecordType.MX: 'Mail Exchange record - routes emails to our servers',
        DNSRecordType.TXT: 'Text record - used for verification and email authentication',
        DNSRecordType.A: 'Address record - points domain to IP address',
        DNSRecordType.CNAME: 'Canonical Name record - creates domain alias',
    }
    return descriptions.get(record_type, 'DNS record')
