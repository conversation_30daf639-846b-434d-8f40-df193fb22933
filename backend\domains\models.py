"""
Domain models using Firestore as backend.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import re


class DomainStatus(Enum):
    PENDING = "pending"
    VERIFYING = "verifying"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    EXPIRED = "expired"


class DNSRecordType(Enum):
    MX = "MX"
    A = "A"
    AAAA = "AAAA"
    CNAME = "CNAME"
    TXT = "TXT"
    SPF = "SPF"
    DKIM = "DKIM"
    DMARC = "DMARC"


@dataclass
class DNSRecord:
    """DNS record model."""
    record_type: DNSRecordType
    name: str
    value: str
    priority: Optional[int] = None
    ttl: int = 3600
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Firestore."""
        data = {
            'type': self.record_type.value,
            'name': self.name,
            'value': self.value,
            'ttl': self.ttl,
        }
        if self.priority is not None:
            data['priority'] = self.priority
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DNSRecord':
        """Create DNSRecord from dictionary."""
        return cls(
            record_type=DNSRecordType(data['type']),
            name=data['name'],
            value=data['value'],
            priority=data.get('priority'),
            ttl=data.get('ttl', 3600),
        )


@dataclass
class Domain:
    """Domain model for Firestore."""
    name: str
    owner_id: str
    status: DomainStatus = DomainStatus.PENDING
    mx_records: List[DNSRecord] = field(default_factory=list)
    dns_records: List[DNSRecord] = field(default_factory=list)
    verification_token: Optional[str] = None
    verification_method: str = "dns"  # dns, file, email
    email_accounts_count: int = 0
    storage_used_mb: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    verified_at: Optional[datetime] = None
    id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Firestore."""
        data = {
            'name': self.name,
            'owner_id': self.owner_id,
            'status': self.status.value,
            'mx_records': [record.to_dict() for record in self.mx_records],
            'dns_records': [record.to_dict() for record in self.dns_records],
            'verification_token': self.verification_token,
            'verification_method': self.verification_method,
            'email_accounts_count': self.email_accounts_count,
            'storage_used_mb': self.storage_used_mb,
        }
        
        if self.created_at:
            data['created_at'] = self.created_at
        if self.updated_at:
            data['updated_at'] = self.updated_at
        if self.verified_at:
            data['verified_at'] = self.verified_at
            
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], doc_id: str = None) -> 'Domain':
        """Create Domain from Firestore document."""
        mx_records = [DNSRecord.from_dict(record) for record in data.get('mx_records', [])]
        dns_records = [DNSRecord.from_dict(record) for record in data.get('dns_records', [])]
        
        return cls(
            id=doc_id,
            name=data['name'],
            owner_id=data['owner_id'],
            status=DomainStatus(data.get('status', 'pending')),
            mx_records=mx_records,
            dns_records=dns_records,
            verification_token=data.get('verification_token'),
            verification_method=data.get('verification_method', 'dns'),
            email_accounts_count=data.get('email_accounts_count', 0),
            storage_used_mb=data.get('storage_used_mb', 0),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at'),
            verified_at=data.get('verified_at'),
        )
    
    @property
    def is_verified(self) -> bool:
        """Check if domain is verified."""
        return self.status in [DomainStatus.ACTIVE, DomainStatus.SUSPENDED]
    
    @property
    def is_active(self) -> bool:
        """Check if domain is active."""
        return self.status == DomainStatus.ACTIVE
    
    def get_required_mx_records(self, mail_server: str = "mail.emailservice.local") -> List[DNSRecord]:
        """Get required MX records for this domain."""
        return [
            DNSRecord(
                record_type=DNSRecordType.MX,
                name=self.name,
                value=mail_server,
                priority=10,
                ttl=3600
            )
        ]
    
    def get_required_txt_records(self) -> List[DNSRecord]:
        """Get required TXT records (SPF, DMARC, verification)."""
        records = []
        
        # SPF Record
        records.append(DNSRecord(
            record_type=DNSRecordType.TXT,
            name=self.name,
            value="v=spf1 mx ~all",
            ttl=3600
        ))
        
        # DMARC Record
        records.append(DNSRecord(
            record_type=DNSRecordType.TXT,
            name=f"_dmarc.{self.name}",
            value="v=DMARC1; p=quarantine; rua=mailto:<EMAIL>",
            ttl=3600
        ))
        
        # Domain verification record
        if self.verification_token:
            records.append(DNSRecord(
                record_type=DNSRecordType.TXT,
                name=f"_emailservice-verification.{self.name}",
                value=f"emailservice-verification={self.verification_token}",
                ttl=300
            ))
        
        return records
    
    @staticmethod
    def is_valid_domain(domain_name: str) -> bool:
        """Validate domain name format."""
        pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(pattern, domain_name)) and len(domain_name) <= 253
    
    def can_add_email_account(self, user_plan: str = "free") -> bool:
        """Check if domain can add more email accounts based on plan."""
        limits = {
            "free": 5,
            "basic": 25,
            "professional": 100,
            "enterprise": 1000,
        }
        return self.email_accounts_count < limits.get(user_plan, 5)


@dataclass
class DomainVerification:
    """Domain verification tracking."""
    domain_id: str
    method: str  # dns, file, email
    token: str
    status: str  # pending, verified, failed
    attempts: int = 0
    last_attempt: Optional[datetime] = None
    verified_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Firestore."""
        data = {
            'domain_id': self.domain_id,
            'method': self.method,
            'token': self.token,
            'status': self.status,
            'attempts': self.attempts,
            'error_message': self.error_message,
        }
        
        if self.last_attempt:
            data['last_attempt'] = self.last_attempt
        if self.verified_at:
            data['verified_at'] = self.verified_at
            
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DomainVerification':
        """Create DomainVerification from dictionary."""
        return cls(
            domain_id=data['domain_id'],
            method=data['method'],
            token=data['token'],
            status=data['status'],
            attempts=data.get('attempts', 0),
            last_attempt=data.get('last_attempt'),
            verified_at=data.get('verified_at'),
            error_message=data.get('error_message'),
        )
